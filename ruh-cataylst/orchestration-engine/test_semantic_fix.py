#!/usr/bin/env python3
"""
Test script to verify the semantic type detection fixes.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.semantic_type_extractor import detect_semantic_type_from_data, extract_semantic_type
from app.utils.helper_functions import format_execution_result


def test_data_driven_detection():
    """Test the new data-driven semantic type detection."""
    print("Testing data-driven semantic type detection...")
    
    test_cases = [
        # Video URLs
        ("https://videos.pexels.com/video-files/5139201/5139201-hd_1920_1080_30fps.mp4", "video"),
        # Audio URLs  
        ("https://example.com/audio.mp3", "audio"),
        # Image URLs
        ("https://example.com/image.jpg", "image"),
        # Regular URLs
        ("https://example.com", "url"),
        # Email
        ("<EMAIL>", "email"),
        # Numbers
        (42, "number"),
        (3.14, "number"),
        (2, "number"),
        (5, "number"),
        (8.5, "number"),
        (16.5, "number"),
        (18, "number"),
        # Strings
        ("pilots in cockpit crisis management", "string"),
        ("video/mp4", "string"),
        # Arrays
        (["item1", "item2"], "array"),
        # Objects
        ({"key": "value"}, "object"),
        # Boolean
        (True, "boolean"),
        (False, "boolean"),
    ]
    
    for data, expected in test_cases:
        result = detect_semantic_type_from_data(data)
        status = "✓" if result == expected else "✗"
        print(f"{status} {data} -> {result} (expected: {expected})")
        if result != expected:
            print(f"   MISMATCH: got '{result}', expected '{expected}'")


def test_format_execution_result_with_video_data():
    """Test format_execution_result with video clip data like in the user's example."""
    print("\nTesting format_execution_result with video data...")
    
    # Simulate the schema for stock video clips
    output_schema = {
        "predefined_fields": [
            {
                "field_name": "stock_video_clips",
                "data_type": {
                    "type": "array",
                    "description": "List of stock video clips",
                    "items": {
                        "type": "object",
                        "properties": {
                            "at_time": {
                                "type": "number",
                                "description": "Time at which the video clip starts"
                            },
                            "url": {
                                "type": "string", 
                                "description": "URL of the stock video clip"
                            },
                            "search_terms": {
                                "type": "array",
                                "description": "Search terms for the video",
                                "items": {
                                    "type": "string"
                                }
                            },
                            "mimetype": {
                                "type": "string",
                                "description": "MIME type of the video"
                            }
                        }
                    }
                }
            }
        ]
    }
    
    # Test data similar to user's example
    execution_result = [{
        "stock_video_clips": [
            {
                "at_time": 2,
                "url": "https://videos.pexels.com/video-files/5139201/5139201-hd_1920_1080_30fps.mp4",
                "search_terms": [
                    "pilots in cockpit crisis management",
                    "aircraft cockpit dynamic shot", 
                    "pilot monitoring controls"
                ],
                "mimetype": "video/mp4"
            },
            {
                "at_time": 5,
                "url": "https://videos.pexels.com/video-files/6739595/6739595-hd_1920_1080_24fps.mp4",
                "search_terms": [
                    "pilot in flight simulator training",
                    "focused expression pilot simulator",
                    "flight simulator control panel"
                ],
                "mimetype": "video/mp4"
            }
        ]
    }]
    
    result = format_execution_result(output_schema, execution_result)
    
    print(f"Result structure: {len(result)} items")
    
    # Check the main array
    main_array = result[0]
    print(f"Main array - property_name: {main_array['property_name']}, data_type: {main_array['data_type']}, semantic_type: {main_array['semantic_type']}")
    
    # Check first video clip object
    first_clip = main_array['data'][0]
    print(f"First clip - property_name: {first_clip['property_name']}, data_type: {first_clip['data_type']}, semantic_type: {first_clip['semantic_type']}")
    
    # Check individual fields in first clip
    for field in first_clip['data']:
        prop_name = field['property_name']
        data_type = field['data_type']
        semantic_type = field['semantic_type']
        data_value = field['data']
        print(f"  {prop_name}: {data_value} -> data_type: {data_type}, semantic_type: {semantic_type}")
        
        # Verify expected semantic types
        if prop_name == "at_time":
            expected = "number"
            status = "✓" if semantic_type == expected else "✗"
            print(f"    {status} at_time should be 'number', got '{semantic_type}'")
        elif prop_name == "url":
            expected = "video"  # Should detect video from URL
            status = "✓" if semantic_type == expected else "✗"
            print(f"    {status} url should be 'video', got '{semantic_type}'")
        elif prop_name == "search_terms":
            expected = "array"
            status = "✓" if semantic_type == expected else "✗"
            print(f"    {status} search_terms should be 'array', got '{semantic_type}'")
        elif prop_name == "mimetype":
            expected = "string"
            status = "✓" if semantic_type == expected else "✗"
            print(f"    {status} mimetype should be 'string', got '{semantic_type}'")


if __name__ == "__main__":
    print("🚀 Testing Semantic Type Detection Fixes")
    print("=" * 50)
    
    test_data_driven_detection()
    test_format_execution_result_with_video_data()
    
    print("\n" + "=" * 50)
    print("🎉 Test completed!")
